/*
 *  Copyright 2019-2025 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.jinghang.cash.modules.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghang.cash.api.dto.GuaranteeConfigDto;
import com.jinghang.cash.api.enums.AbleStatusExt;
import com.jinghang.cash.modules.project.domain.GuaranteeConfig;
import com.jinghang.cash.modules.project.domain.dto.GuaranteeConfigQueryCriteria;
import com.jinghang.cash.modules.project.mapper.GuaranteeConfigMapper;
import com.jinghang.cash.modules.project.service.GuaranteeConfigService;
import com.jinghang.cash.utils.PageResult;
import com.jinghang.cash.utils.PageUtil;
import com.jinghang.cash.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 服务实现
 * @date 2025-08-22
 **/
@Service
@RequiredArgsConstructor
public class GuaranteeConfigServiceImpl extends ServiceImpl<GuaranteeConfigMapper, GuaranteeConfig> implements GuaranteeConfigService {

    private final GuaranteeConfigMapper guaranteeConfigMapper;

    @Override
    public GuaranteeConfig selectByid(String id) {
        GuaranteeConfig guaranteeConfig = guaranteeConfigMapper.selectById(id);
        return guaranteeConfig;
    }

    @Override
    public PageResult<GuaranteeConfig> selectAll(GuaranteeConfigQueryCriteria criteria, Page<Object> page) {
        //创建分页对象（pageNum：页码，pageSize：每页条数）
        Page<GuaranteeConfig> capitalpage = new Page<>(criteria.getPageNum(), criteria.getPageSize());
        QueryWrapper<GuaranteeConfig> queryWrapper = new QueryWrapper<>();
        if (criteria.getGuaranteeCode() != null && !criteria.getGuaranteeCode().isEmpty()) {
            queryWrapper.eq("guarantee_code", criteria.getGuaranteeCode());
        }
        if (criteria.getGuaranteeName() != null && !criteria.getGuaranteeName().isEmpty()) {
            queryWrapper.like("guarantee_name", criteria.getGuaranteeName());
        }
        if (criteria.getEnabled() != null && !criteria.getEnabled().isEmpty()) {
            queryWrapper.eq("enabled", criteria.getEnabled());
        }
        Page<GuaranteeConfig> guaranteeConfigPage = guaranteeConfigMapper.selectPage(capitalpage, queryWrapper);
        return PageUtil.toPage(guaranteeConfigPage.getRecords(),guaranteeConfigPage.getTotal());
    }

//    @Override
//    public List<GuaranteeConfig> queryAll(GuaranteeConfigQueryCriteria criteria){
//        return guaranteeConfigMapper.findAll(criteria);
//        IPage<GuaranteeConfig> all = guaranteeConfigMapper.findAll(criteria, page);
//        List<GuaranteeConfig> records = all.getRecords();
//        for (GuaranteeConfig record : records) {
//
//        }
//    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(GuaranteeConfig resources) {
        String id = "G" + System.currentTimeMillis() + String.valueOf(Math.random()).substring(2,5) ;
        resources.setId(id);
        resources.setCreatedBy(String.valueOf(SecurityUtils.getCurrentUserId()));
        resources.setCreatedTime(LocalDateTime.now());
        resources.setEnabled(AbleStatusExt.INIT.name());
        guaranteeConfigMapper.insert(resources);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(GuaranteeConfig resources) {
        GuaranteeConfig guaranteeConfig = getById(resources.getId());
        guaranteeConfig.copy(resources);
        guaranteeConfig.setUpdatedTime(LocalDateTime.now());
        guaranteeConfig.setUpdatedBy(String.valueOf(SecurityUtils.getCurrentUserId()));
        guaranteeConfigMapper.updateById(guaranteeConfig);
    }

    @Override
    public void enable(GuaranteeConfigDto resources) {
        GuaranteeConfig guaranteeConfig = getById(resources.getId());
        guaranteeConfig.setUpdatedTime(LocalDateTime.now());
        guaranteeConfig.setUpdatedBy(String.valueOf(SecurityUtils.getCurrentUserId()));
        guaranteeConfig.setEnabled(resources.getEnabled());
        guaranteeConfigMapper.updateById(guaranteeConfig);
    }
}
