/*
*  Copyright 2019-2025 <PERSON>e
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghang.cash.api.dto.CapitalConfigDto;
import com.jinghang.cash.api.enums.AbleStatusExt;
import com.jinghang.cash.modules.project.domain.CapitalConfig;
import com.jinghang.cash.modules.project.domain.dto.CapitalConfigQueryCriteria;
import com.jinghang.cash.modules.project.mapper.CapitalConfigMapper;
import com.jinghang.cash.modules.project.service.CapitalConfigService;
import com.jinghang.cash.utils.PageResult;
import com.jinghang.cash.utils.PageUtil;
import com.jinghang.cash.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;


/**
* @description 服务实现
* <AUTHOR>
* @date 2025-08-22
**/
@Service
@RequiredArgsConstructor
public class CapitalConfigServiceImpl extends ServiceImpl<CapitalConfigMapper, CapitalConfig> implements CapitalConfigService {


    private final CapitalConfigMapper capitalConfigMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CapitalConfig selectByid(String id) {
        return capitalConfigMapper.selectById(id);
    }

    @Override
    public PageResult<CapitalConfig> selectAll(CapitalConfigQueryCriteria criteria, Page<Object> page){
        //创建分页对象（pageNum：页码，pageSize：每页条数）
        Page<CapitalConfig> capitalpage = new Page<>(criteria.getPageNum(),criteria.getPageSize());
        QueryWrapper<CapitalConfig> queryWrapper = new QueryWrapper<>();
        if (criteria.getBankChannel() != null && !criteria.getBankChannel().isEmpty()){
            queryWrapper.eq("bank_channel",criteria.getBankChannel());
        }
        if (criteria.getCapitalName() != null && !criteria.getCapitalName().isEmpty()){
            queryWrapper.like("capital_name",criteria.getCapitalName());
        }
        if (criteria.getEnabled() != null && !criteria.getEnabled().isEmpty()){
            queryWrapper.eq("enabled",criteria.getEnabled());
        }
        Page<CapitalConfig> capitalConfigPage = capitalConfigMapper.selectPage(capitalpage, queryWrapper);
        return PageUtil.toPage(capitalConfigPage.getRecords(),capitalConfigPage.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(CapitalConfig resources) {
        String id = "C" + System.currentTimeMillis() + String.valueOf(Math.random()).substring(2,5) ;
        resources.setId(id);
        resources.setCreatedBy(String.valueOf(SecurityUtils.getCurrentUserId()));
        resources.setCreatedTime(LocalDateTime.now());
        resources.setEnabled(AbleStatusExt.INIT.name());
        capitalConfigMapper.insert(resources);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(CapitalConfig resources) {
        CapitalConfig capitalConfig = getById(resources.getId());
        capitalConfig.copy(resources);
        capitalConfig.setUpdatedBy(String.valueOf(SecurityUtils.getCurrentUserId()));
        capitalConfig.setUpdatedTime(LocalDateTime.now());
        capitalConfigMapper.updateById(capitalConfig);
    }


    @Override
    public void enable(CapitalConfigDto resources) {
        CapitalConfig capitalConfig = getById(resources.getId());
        capitalConfig.setUpdatedBy(String.valueOf(SecurityUtils.getCurrentUserId()));
        capitalConfig.setUpdatedTime(LocalDateTime.now());
        capitalConfig.setEnabled(resources.getEnabled());
        capitalConfigMapper.updateById(capitalConfig);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAll(List<String> ids) {
        capitalConfigMapper.deleteBatchIds(ids);
    }

}