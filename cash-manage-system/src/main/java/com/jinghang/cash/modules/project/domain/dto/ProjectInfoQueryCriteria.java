/*
*  Copyright 2019-2025 <PERSON>e
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.project.domain.dto;

import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
* <AUTHOR>
* @date 2025-08-25
**/
@Data
public class ProjectInfoQueryCriteria{

    @ApiModelProperty(value = "页码")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页数据量")
    private Integer pageSize = 10 ;

    @ApiModelProperty(value = "项目唯一编码")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "资产方编码 (关联资产方表)")
    private String flowChannel;

    @ApiModelProperty(value = "资金方编码 (关联资金方表)")
    private String capitalChannel;

    @ApiModelProperty(value = "项目类型编码 (关联项目类型表)")
    private String projectTypeCode;

    @ApiModelProperty(value = "项目状态")
    private String enabled;

    @ApiModelProperty(value = "项目时效类型")
    private String projectDurationType;



}