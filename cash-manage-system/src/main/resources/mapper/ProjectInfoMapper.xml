<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.cash.modules.project.mapper.ProjectInfoMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.jinghang.cash.modules.project.domain.ProjectInfo">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="project_code" property="projectCode" jdbcType="VARCHAR"/>
        <result column="project_name" property="projectName" jdbcType="VARCHAR"/>
        <result column="flow_channel" property="flowChannel" jdbcType="VARCHAR"/>
        <result column="guarantee_code" property="guaranteeCode" jdbcType="VARCHAR"/>
        <result column="capital_channel" property="capitalChannel" jdbcType="VARCHAR"/>
        <result column="project_type_code" property="projectTypeCode" jdbcType="VARCHAR"/>
        <result column="enabled" property="enabled" jdbcType="VARCHAR"/>
        <result column="start_date" property="startDate" jdbcType="DATE"/>
        <result column="end_date" property="endDate" jdbcType="DATE"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="revision" property="revision" jdbcType="INTEGER"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, project_code, project_name, flow_channel, guarantee_code, capital_channel,
        project_type_code, enabled, start_date, end_date, remark, revision,
        created_by, created_time, updated_by, updated_time
    </sql>

    <!-- 根据项目编码查询项目信息 -->
    <select id="selectByProjectCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM project_info
        WHERE project_code = #{projectCode,jdbcType=VARCHAR}
        AND enabled = 'ENABLE'
        LIMIT 1
    </select>

    <!-- 查询所有启用状态的项目信息 -->
    <select id="selectAllEnabledProjects" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM project_info
        WHERE enabled = 'ENABLE'
        ORDER BY created_time DESC
    </select>
    <select id="queryProjectInfoPage"
            resultType="com.jinghang.cash.modules.project.service.domain.vo.ProjectInfoVo">
        select t1.*,
        t2.capital_name_short as capitalChannelName,
        t5.daily_credit_limit as dailyCreditLimit,
        t5.daily_loan_limit as dailyLoanLimit,
        t5.project_duration_type as projectDurationType
        from  project_info as t1
        LEFT JOIN capital_config as t2 on t1.capital_channel = t2.bank_channel
        LEFT JOIN flow_config as t3 on t1.flow_channel = t3.flow_channel
        LEFT JOIN guarantee_config as t4 on t1.guarantee_code = t4.guarantee_code
        LEFT JOIN project_elements as t5 on t1.project_code = t5.project_code
        <where>
            <if test="param.projectCode != null and param.projectCode != ''">
                and t1.project_code = #{param.projectCode}
            </if>
            <if test="param.projectName != null and param.projectName != ''">
                and t1.project_name = #{param.projectName}
            </if>
            <if test="param.projectTypeCode != null and param.projectTypeCode != ''">
                and t1.project_type_code = #{param.projectTypeCode}
            </if>
            <if test="param.enabled != null and param.enabled != ''">
                and t1.enabled = #{param.enabled}
            </if>
            <if test="param.capitalChannel != null and param.capitalChannel != ''">
                and t4.capital_name like concat('%', #{param.capitalChannel}, '%')
            </if>
            <if test="param.flowChannel != null and param.flowChannel != ''">
                and t3.flow_name like concat('%',#{param.flowChannel}, '%')
            </if>
            <if test="param.projectDurationType != null and param.projectDurationType != ''">
                and t5.project_duration_type = #{param.projectDurationType}
            </if>
        </where>
        order by t1.created_time desc
    </select>


    <select id="queryTemProjectInfoPage"
            resultType="com.jinghang.cash.modules.project.service.domain.vo.ProjectInfoVo">
        select t1.*,
        t5.temp_start_time as tempStartTime,
        t5.temp_end_time as tempEndTime,
        t5.remark as temRemark,
        t5.id as temId,
        t5.enabled as temEnabled
        from  project_info as t1
        LEFT JOIN project_elements as t5 on t1.project_code = t5.project_code
        <where>
            <if test="param.projectCode != null and param.projectCode != ''">
                and t1.project_code = #{param.projectCode}
            </if>
            <if test="param.projectName != null and param.projectName != ''">
                and t1.project_name = #{param.projectName}
            </if>
            <if test="param.projectTypeCode != null and param.projectTypeCode != ''">
                and t1.project_type_code = #{param.projectTypeCode}
            </if>
            <if test="param.enabled != null and param.enabled != ''">
                and t1.enabled = #{param.enabled}
            </if>
            <if test="param.projectDurationType != null and param.projectDurationType != ''">
                and t5.project_duration_type = #{param.projectDurationType}
            </if>
        </where>
        order by t1.created_time desc
    </select>



</mapper>
