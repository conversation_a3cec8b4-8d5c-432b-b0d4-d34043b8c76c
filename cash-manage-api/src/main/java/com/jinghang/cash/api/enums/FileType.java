package com.jinghang.cash.api.enums;

/**
 * <AUTHOR>
 * @date 2023/9/21
 */
public enum FileType {
    ID_HEAD("身份证人头面"),
    ID_NATION("身份证国徽面"),
    ID_FACE("活体人脸"),
    //    咨询服务合同(超捷).pdf
    CONSULTING_SERVICE_CONTRACT("咨询服务合同(超捷)"),
    //    个人敏感信息授权书.pdf
    PERSONAL_INFORMATION_AUTHORIZATION_LETTER("个人敏感信息授权书"),


    //    综合授权书-担保（四合一）.pdf
    COMPREHENSIVE_AUTHORIZATION_LETTER_GUARANTEE(" 综合授权书-担保（四合一）"),

    //    委托扣款授权书-担保vs借款人.pdf
    AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE("委托扣款授权书-担保vs借款人"),

    //    综合授权书（通用）.pdf
    COMPREHENSIVE_AUTHORIZATION_LETTER_COMMON("综合授权书（通用）"),


    //    数字证书使用授权协议.pdf
    DIGITAL_CERTIFICATE_USAGE_AUTHORIZATION_AGREEMENT("个人敏感信息授权书"),

    //    承诺书.pdf
    LETTER_OF_COMMITMENT("承诺书.pdf"),

//    借款合同及贷款告知事项客户声明书.pdf


    GEOGRAPHICAL_COMMITMENT("地域承诺函"),

    LOAN_FILE("放款对账文件"),
    REPAYMENT_FILE("还款对账文件"),
    COMPENSATION_INNER_MARK_FILE("代偿标记文件"),
    COMPENSATION_FILE("代偿文件"),
    REPURCHASE_FILE("回购文件"),

    FACE_AUTH_APPROVAL("人脸认证服务授权书"),
    SENSITIVE_PERSONAL_INFORMATION_HANDLE_LETTER("敏感个人信息处理授权书"),

    BORROWER_IMPORTANT_INFORMATION_TIPS("重要提示"),
    DUE_FILE("还款计划担保费文件"),
    LOAN_VOUCHER_FILE("放款凭证文件"),
    DIGITAL_CERTIFICATE_AUTHORIZATION_LETTER("数字证书授权使用书"),
    DEBT_CONFIRMATION_AGREEMENT("债权确认协议"),
    PERSONAL_LOAN_CUSTOMER_COMMIT_CONFIRMATION("个人贷款客户承诺确认书"),
    CREDIT_SETTLE_VOUCHER_FILE("结清证明文件"),
    COLLECTION_AGREEMENT("代收付协议"),
    PERSONAL_LOAN_USE_COMMITMENT("个人贷款用途承诺书"),
    SYNTHESIS_AUTHORIZATION("综合授权书"),

    ENTRUSTED_DEDUCTION_LETTER("个人客户扣款授权书"),
    LOAN_CONTRACT("借款合同"),
    // 添加征信授权书
    PERSONAL_CREDIT_AUTHORIZATION_LETTER_CREDIT("征信授权书"),

    // 添加仲裁协议
    ARBITRATION_AGREEMENT("仲裁协议"),
    ;

    private String desc;

    FileType(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }
}
